"""
Story Deconstructor - Implements the "Internal State Extraction" pipeline.
Analyzes stories to extract internal states, emotions, and psychological patterns.

This module implements a robust, two-phase pipeline:
Phase 1: Parallel Foundational Extraction (Trigger, Feeling, Thought)
Phase 2: Sequential Context-Aware Enrichment (Violated Value)
"""

import json
import logging
from typing import Dict, List, Any
from core.llm_service import llm_service
from core.supabase_client import supabase_client
from core.utils import load_prompts
from core.models import Story, StoryAnalysis

logger = logging.getLogger(__name__)


class StoryDeconstructor:
    """Handles the analysis and deconstruction of personal stories using a two-phase pipeline."""

    def __init__(self):
        """Initialize the story deconstructor with prompts."""
        self.prompts = load_prompts()
        self.extraction_schema = self._get_extraction_schema()

    def _get_extraction_schema(self) -> Dict[str, Any]:
        """Get the JSON schema for structured extraction responses."""
        return {
            "type": "object",
            "properties": {
                "story_id": {
                    "type": "string",
                    "description": "Unique story identifier."
                },
                "extraction_results": {
                    "type": "object",
                    "description": "Results of the emotional extraction process.",
                    "properties": {
                        "trigger": {
                            "type": ["object", "null"],
                            "description": "Details about the triggering event.",
                            "properties": {
                                "title": {
                                    "type": "string",
                                    "description": "Title of the triggering event."
                                },
                                "description": {
                                    "type": "string",
                                    "description": "Description of the triggering event."
                                },
                                "category": {
                                    "type": "string",
                                    "description": "Category of the event.",
                                    "enum": ["Social Interaction", "Stressor", "Life Event", "Environmental", "Other"]
                                }
                            },
                            "required": ["title", "description", "category"],
                            "additionalProperties": False
                        },
                        "feelings": {
                            "type": "object",
                            "description": "Emotional responses to the trigger.",
                            "properties": {
                                "emotions": {
                                    "type": "array",
                                    "description": "List of emotions felt.",
                                    "items": {
                                        "type": "string"
                                    }
                                }
                            },
                            "required": ["emotions"],
                            "additionalProperties": False
                        },
                        "thought": {
                            "type": ["object", "null"],
                            "description": "Internal monologue reflecting thoughts on the situation.",
                            "properties": {
                                "internal_monologue": {
                                    "type": "string",
                                    "description": "The narrator's internal thoughts."
                                }
                            },
                            "required": ["internal_monologue"],
                            "additionalProperties": False
                        },
                        "value_analysis": {
                            "type": ["object", "null"],
                            "description": "Analysis of the violated values in the scenario.",
                            "properties": {
                                "violated_value": {
                                    "type": "string",
                                    "description": "The value that has been violated."
                                },
                                "reasoning": {
                                    "type": "string",
                                    "description": "The reasoning behind the violated value."
                                },
                                "confidence_score": {
                                    "type": "integer",
                                    "description": "Confidence score indicating the strength of the analysis.",
                                    "minimum": 1,
                                    "maximum": 5
                                }
                            },
                            "required": ["violated_value", "reasoning", "confidence_score"],
                            "additionalProperties": False
                        }
                    },
                    "required": ["trigger", "feelings", "thought", "value_analysis"],
                    "additionalProperties": False
                }
            },
            "required": ["story_id", "extraction_results"],
            "additionalProperties": False
        }
    
    def _extract_trigger(self, story_text: str) -> Dict[str, Any]:
        """
        Phase 1: Extract the trigger event from the story.

        Args:
            story_text: The raw story text to analyze

        Returns:
            Dictionary containing trigger information or None
        """
        try:
            system_prompt = self.prompts["trigger_extraction"]["system_prompt"]
            user_prompt = self.prompts["trigger_extraction"]["user_prompt"].format(
                story_text=story_text
            )

            response = llm_service.generate_completion(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                response_format="json"
            )

            return llm_service.parse_json_response(response)

        except Exception as e:
            logger.error(f"Error extracting trigger: {e}")
            return None

    def _extract_feelings(self, story_text: str) -> Dict[str, Any]:
        """
        Phase 1: Extract explicitly mentioned emotions from the story.

        Args:
            story_text: The raw story text to analyze

        Returns:
            Dictionary containing emotions list
        """
        try:
            system_prompt = self.prompts["feeling_extraction"]["system_prompt"]
            user_prompt = self.prompts["feeling_extraction"]["user_prompt"].format(
                story_text=story_text
            )

            response = llm_service.generate_completion(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                response_format="json"
            )

            return llm_service.parse_json_response(response)

        except Exception as e:
            logger.error(f"Error extracting feelings: {e}")
            return {"emotions": []}

    def _extract_thought(self, story_text: str) -> Dict[str, Any]:
        """
        Phase 1: Extract internal thoughts/monologue from the story.

        Args:
            story_text: The raw story text to analyze

        Returns:
            Dictionary containing internal monologue or None
        """
        try:
            system_prompt = self.prompts["thought_extraction"]["system_prompt"]
            user_prompt = self.prompts["thought_extraction"]["user_prompt"].format(
                story_text=story_text
            )

            response = llm_service.generate_completion(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                response_format="json"
            )

            return llm_service.parse_json_response(response)

        except Exception as e:
            logger.error(f"Error extracting thought: {e}")
            return None

    def _extract_violated_value(self, story_text: str, trigger: Dict[str, Any],
                               feelings: Dict[str, Any], thought: Dict[str, Any]) -> Dict[str, Any]:
        """
        Phase 2: Extract violated values using context from Phase 1.

        Args:
            story_text: The raw story text
            trigger: Trigger extraction results
            feelings: Feelings extraction results
            thought: Thought extraction results

        Returns:
            Dictionary containing value analysis or None
        """
        try:
            system_prompt = self.prompts["value_extraction"]["system_prompt"]

            context = {
                "story": story_text,
                "trigger": trigger,
                "feelings": feelings,
                "thought": thought
            }

            user_prompt = self.prompts["value_extraction"]["user_prompt"].format(
                context=json.dumps(context, indent=2)
            )

            response = llm_service.generate_completion(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                response_format="json"
            )

            return llm_service.parse_json_response(response)

        except Exception as e:
            logger.error(f"Error extracting violated value: {e}")
            return None

    def analyze_story(self, story_text: str, story_id: str) -> StoryAnalysis:
        """
        Analyze a single story using the two-phase extraction pipeline.

        Phase 1: Parallel extraction of trigger, feelings, and thoughts
        Phase 2: Context-aware extraction of violated values

        Args:
            story_text: The raw story text to analyze
            story_id: The ID of the story being analyzed

        Returns:
            StoryAnalysis instance containing the complete analysis results
        """
        try:
            logger.info(f"Starting two-phase analysis for story {story_id}")

            # Phase 1: Parallel foundational extraction
            logger.debug(f"Phase 1: Extracting foundational elements for story {story_id}")

            # Extract trigger, feelings, and thoughts in parallel
            # For now, we'll do them sequentially but this could be parallelized
            trigger = self._extract_trigger(story_text)
            feelings = self._extract_feelings(story_text)
            thought = self._extract_thought(story_text)

            logger.debug(f"Phase 1 complete for story {story_id}")

            # Phase 2: Context-aware enrichment
            logger.debug(f"Phase 2: Extracting violated values for story {story_id}")

            value_analysis = self._extract_violated_value(
                story_text, trigger, feelings, thought
            )

            logger.debug(f"Phase 2 complete for story {story_id}")

            # Assemble final result
            extraction_results = {
                "trigger": trigger,
                "feelings": feelings,
                "thought": thought,
                "value_analysis": value_analysis
            }

            analysis_data = {
                "story_id": story_id,
                "extraction_results": extraction_results
            }

            # Create StoryAnalysis instance
            story_analysis = StoryAnalysis(
                story_id=story_id,
                analysis=analysis_data,
                analysis_version="2.0"  # Updated version for two-phase pipeline
            )

            logger.info(f"Successfully completed two-phase analysis for story {story_id}")
            return story_analysis

        except Exception as e:
            logger.error(f"Error in two-phase analysis for story {story_id}: {e}")
            raise

    def analyze_story_with_schema(self, story_text: str, story_id: str) -> StoryAnalysis:
        """
        Analyze a story using structured JSON schema for validation.

        Args:
            story_text: The raw story text to analyze
            story_id: The ID of the story being analyzed

        Returns:
            StoryAnalysis instance containing the analysis results with schema validation
        """
        try:
            # Run the two-phase analysis
            story_analysis = self.analyze_story(story_text, story_id)

            # Use structured response with schema if supported
            if hasattr(llm_service, 'generate_structured_response'):
                logger.info(f"Using structured response for story {story_id}")

                system_prompt = """You are a data extraction specialist. Format the provided analysis results according to the exact JSON schema specified."""

                user_prompt = f"""Format the following analysis results according to the JSON schema:

                Analysis Results: {json.dumps(story_analysis.analysis, indent=2)}

                Ensure all fields match the schema exactly and maintain data integrity."""

                structured_response = llm_service.generate_structured_response(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt,
                    schema=self.extraction_schema
                )

                # Update the analysis with structured response
                story_analysis.analysis = structured_response
                story_analysis.raw_response = json.dumps(structured_response)

                return story_analysis
            else:
                # Fallback to regular analysis
                return story_analysis

        except Exception as e:
            logger.error(f"Error in schema-validated analysis for story {story_id}: {e}")
            raise
    
    def analyze_multiple_stories(self, stories: List[Story], use_schema: bool = True) -> List[StoryAnalysis]:
        """
        Analyze multiple stories in batch using the two-phase pipeline.

        Args:
            stories: List of Story instances to analyze
            use_schema: Whether to use structured schema validation

        Returns:
            List of StoryAnalysis instances
        """
        analyses = []

        for story in stories:
            try:
                story_id = str(story.id)
                story_content = story.content

                if not story_content:
                    logger.warning(f"Empty content for story {story_id}")
                    continue

                # Use schema-validated analysis if requested and supported
                if use_schema:
                    analysis = self.analyze_story_with_schema(story_content, story_id)
                else:
                    analysis = self.analyze_story(story_content, story_id)

                analyses.append(analysis)

                # Store analysis in database
                supabase_client.insert_story_analysis(analysis)

            except Exception as e:
                logger.error(f"Error processing story {story.id}: {e}")
                continue

        logger.info(f"Completed two-phase analysis of {len(analyses)} stories")
        return analyses
    
    def extract_key_themes(self, analyses: List[StoryAnalysis]) -> Dict[str, Any]:
        """
        Extract overarching themes from multiple story analyses using the new extraction format.

        Args:
            analyses: List of StoryAnalysis instances from the two-phase pipeline

        Returns:
            Dictionary containing key themes and patterns
        """
        try:
            # Combine all analyses for theme extraction
            extraction_results = []
            for analysis in analyses:
                # Extract the analysis data from the StoryAnalysis object
                analysis_data = analysis.analysis
                if "extraction_results" in analysis_data:
                    extraction_results.append(analysis_data["extraction_results"])
                elif isinstance(analysis_data, dict):  # Direct analysis data
                    extraction_results.append(analysis_data)

            combined_analyses = {
                "total_stories": len(analyses),
                "extraction_results": extraction_results
            }

            system_prompt = """You are an expert at identifying patterns and themes across multiple story analyses.
            Extract overarching themes, recurring patterns, and key insights from the structured extraction results."""

            user_prompt = f"""Based on the following story extraction results, identify:
            1. Recurring trigger patterns and categories
            2. Common emotional responses and patterns
            3. Frequent internal thought patterns and cognitive styles
            4. Most commonly violated values and their themes
            5. Relationships between triggers, emotions, thoughts, and values
            6. Overall psychological patterns and coping mechanisms

            Focus on:
            - Trigger categories that appear most frequently
            - Emotional patterns that repeat across stories
            - Common cognitive responses and thought patterns
            - Core values that are consistently important to this person
            - How different triggers relate to different violated values

            Extraction Results: {json.dumps(combined_analyses, indent=2)}

            Provide a comprehensive analysis in JSON format with specific insights about this person's psychological patterns."""

            response = llm_service.generate_completion(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                response_format="json"
            )

            themes = llm_service.parse_json_response(response)

            logger.info("Successfully extracted key themes from two-phase analyses")
            return themes

        except Exception as e:
            logger.error(f"Error extracting themes: {e}")
            raise
    
    def process_all_stories(self) -> Dict[str, Any]:
        """
        Process all stories in the database through the complete analysis pipeline.

        Returns:
            Summary of the processing results
        """
        try:
            # Get all stories from database (now returns List[Story])
            stories = supabase_client.get_stories()

            if not stories:
                logger.warning("No stories found in database")
                return {"status": "no_stories", "processed": 0}

            # Analyze all stories (now works with Story objects)
            analyses = self.analyze_multiple_stories(stories)

            # Extract key themes (now works with StoryAnalysis objects)
            themes = self.extract_key_themes(analyses)

            result = {
                "status": "success",
                "total_stories": len(stories),
                "processed_stories": len(analyses),
                "key_themes": themes
            }

            logger.info(f"Story processing complete: {result}")
            return result

        except Exception as e:
            logger.error(f"Error in story processing pipeline: {e}")
            raise


# Global story deconstructor instance
story_deconstructor = StoryDeconstructor()
